import { Tag } from 'antd';
import React from 'react';
import Markdown from 'markdown-to-jsx';
import moment from 'moment';
import { PTA_MOMENT_DATE_FORMAT, currentStatusFilters } from './constants';
import StatusCustomFilter from './CustomSelectFilter';
import MultiSelectEpicFilter from './MultiSelectEpicFilter';
import { getColorBasedOnStatus } from './helper';

const dateFormatter = date => {
  if (date && date.value) {
    return moment(date.value).format(PTA_MOMENT_DATE_FORMAT);
  }
  return '';
};

// Number formatter to ensure values are rounded to 2 decimal places
const numberFormatter = params => {
  if (params.value !== undefined && params.value !== null) {
    return Number(params.value).toFixed(2);
  }
  return '';
};

const userStoryRenderer = (params, showModal) => {
  if (params.data) {
    return params.data.userStory.length > 15 ? (
      <span>
        {`${params.data.userStory.substring(0, 15)}...`}{' '}
        <span
          style={{ cursor: 'pointer', color: 'blue' }}
          onClick={() => showModal(params.data.userStory, 'User Story')}
          data-testid="view-more-story"
          role="none"
        >
          View more
        </span>
      </span>
    ) : (
      params.data.userStory
    );
  }
  return '-';
};

const functionalFlowRenderer = (params, showModal) => {
  if (params.data && params.data.functionalFlow) {
    return params.data.functionalFlow.length > 15 ? (
      <span>
        {`${params.data.functionalFlow.substring(0, 15)}...`}{' '}
        <span
          style={{ cursor: 'pointer', color: 'blue' }}
          onClick={() =>
            showModal(
              <Markdown>{params.data.functionalFlow}</Markdown>,
              'Functional Flow',
            )
          }
          role="none"
        >
          View more
        </span>
      </span>
    ) : (
      params.data.functionalFlow
    );
  }
  return '-';
};

export const renderDataWithRAGColor = (deviation, threshold) => {
  // when deviation is >= 0; green
  if (deviation >= 0) {
    return <div className="deviation green-deviation-bg">{deviation}</div>;
  }

  // when deviation is < 0 and > threshold; red
  if (Math.abs(deviation) > threshold) {
    return <div className="deviation red-deviation-bg">{deviation}</div>;
  }

  // amber
  return <div className="deviation amber-deviation-bg">{deviation}</div>;
};

const epicDeviationRenderer = params => {
  if (params.data) {
    const epicDeviation = params.data.epicDeviation
      ? params.data.epicDeviation
      : 0;
    const salesEstimation = params.data.salesEstimation
      ? params.data.salesEstimation
      : 0;
    const salesEstimationThreshold = salesEstimation / 10;

    return renderDataWithRAGColor(epicDeviation, salesEstimationThreshold);
  }
  return '-';
};

const salesDeviationRenderer = params => {
  if (params.data) {
    const salesDeviation = params.data.salesDeviation
      ? params.data.salesDeviation
      : 0;
    const salesEstimation = params.data.salesEstimation
      ? params.data.salesEstimation
      : 0;
    const salesEstimationThreshold = salesEstimation / 10;

    return renderDataWithRAGColor(salesDeviation, salesEstimationThreshold);
  }
  return '-';
};

export const getColumnPropsForTrackerSheet = (
  sprintList,
  epicStatus,
  showModal,
) => [
  {
    headerName: 'Epic Names',
    field: 'epicName',
    width: '250px',
    pinned: 'left',
    cellStyle: params => ({
      height: params.data && params.data.span > 0 ? 'fit-content' : '0px',
    }),
    cellRenderer: data => {
      const epicName =
        data.data && data.data.epicName ? data.data.epicName : '';
      const jiraEpicUrl =
        data.data && data.data.jiraEpicUrl ? data.data.jiraEpicUrl : '';

      return (
        <a
          style={{ color: 'initial', cursor: 'pointer' }}
          href={jiraEpicUrl}
          target="_blank"
        >
          {epicName}
        </a>
      );
    },
    rowSpan: params => (params.data ? params.data.span : 0),
    filter: true,
    floatingFilter: true,
    floatingFilterComponent: MultiSelectEpicFilter,
    floatingFilterComponentParams: {
      suppressFilterButton: true,
      field: 'epicName',
    },
  },
  {
    headerName: 'Epic status',
    field: 'epicStatus',
    width: '140px',
    headerTooltip: 'Epic Status',
    cellStyle: params => ({
      height: params.data && params.data.span > 0 ? 'fit-content' : '0px',
    }),
    filter: true,
    floatingFilter: true,
    floatingFilterComponent: StatusCustomFilter,
    floatingFilterComponentParams: {
      suppressFilterButton: true,
      field: 'epicStatus',
      selectOptions: currentStatusFilters,
    },
    rowSpan: params => (params.data ? params.data.span : 0),
    cellRenderer: param => {
      if (!param.data) {
        return null;
      }
      const epicStatusValue = Array.isArray(epicStatus)
        ? epicStatus.find(epic => epic.jiraEpicId === param.data.jiraEpicId)
        : '';
      const color = getColorBasedOnStatus(epicStatusValue.status);
      return (
        <div className="deviation" style={{ backgroundColor: color }}>
          {epicStatusValue.status}
        </div>
      );
    },
    sortable: true,
  },
  {
    headerName: 'Sales Estimate (Hrs)',
    field: 'salesEstimation',
    type: 'rightAligned',
    width: '180px',
    cellStyle: params => ({
      height: params.data && params.data.span > 0 ? 'fit-content' : '0px',
    }),
    rowSpan: params => (params.data ? params.data.span : 0),
    sortable: true,
  },
  {
    headerName: 'Epic Deviation',
    field: 'epicDeviation',
    width: '140px',
    headerTooltip: 'Epic Deviation = SalesEstimate - TotalLoggedEfforts',
    cellStyle: params => ({
      height: params.data && params.data.span > 0 ? 'fit-content' : '0px',
    }),
    rowSpan: params => (params.data ? params.data.span : 0),
    cellRenderer: epicDeviationRenderer,
    sortable: true,
  },
  {
    headerName: 'Sprint Name',
    field: 'sprintName',
    cellRenderer: data => {
      const sprintName =
        data.data && data.data.sprintName ? data.data.sprintName : '';
      const sprintUrl =
        data.data && data.data.sprintUrl ? data.data.sprintUrl : '#';

      return (
        <a style={{ color: 'blue' }} href={sprintUrl || '#'} target="_blank">
          {sprintName}
        </a>
      );
    },
    filter: true,
    floatingFilter: true,
    floatingFilterComponent: StatusCustomFilter,
    floatingFilterComponentParams: {
      suppressFilterButton: true,
      field: 'sprintName',
      selectOptions: sprintList,
    },
  },
  {
    headerName: 'Story Number',
    field: 'storyNumber',
    cellRenderer: data => {
      const storyNumber =
        data.data && data.data.storyNumber ? data.data.storyNumber : '';
      const storyLink =
        data.data && data.data.storyLink ? data.data.storyLink : '';

      return (
        <a style={{ color: 'blue' }} href={storyLink} target="_blank">
          {storyNumber}
        </a>
      );
    },
    sortable: true,
    filter: true,
    floatingFilter: true,
    floatingFilterComponentParams: {
      suppressFilterButton: true,
    },
  },
  {
    headerName: 'Status',
    field: 'status',
    cellRenderer: data => {
      const status = data.data && data.data.status ? data.data.status : '';
      return <Tag color={getColorBasedOnStatus(status)}>{status}</Tag>;
    },
    filter: true,
    floatingFilter: true,
    floatingFilterComponent: StatusCustomFilter,
    floatingFilterComponentParams: {
      suppressFilterButton: true,
      field: 'status',
      selectOptions: currentStatusFilters,
    },
  },
  {
    headerName: 'Dev Re-Estimation',
    field: 'developerOriginalEstimation',
    type: 'rightAligned',
    sortable: true,
  },
  {
    headerName: 'Dev Spent',
    field: 'developerSpent',
    type: 'rightAligned',
    sortable: true,
  },
  {
    headerName: 'Dev Deviation',
    headerTooltip: 'Dev Deviation = Dev Re-Estimation - Dev Spent',
    field: 'developerDeviation',
    type: 'rightAligned',
    valueFormatter: numberFormatter,
    sortable: true,
  },
  {
    headerName: 'Start Date',
    field: 'startDate',
    type: 'rightAligned',
    sortable: true,
    valueFormatter: dateFormatter,
  },
  {
    headerName: 'End Date',
    field: 'endDate',
    type: 'rightAligned',
    sortable: true,
    valueFormatter: dateFormatter,
  },
  {
    headerName: 'User Story',
    field: 'userStory',
    width: '260px',
    cellRenderer: params => userStoryRenderer(params, showModal),
  },
  {
    headerName: 'Functional Flow',
    field: 'functionalFlow',
    width: '260px',
    cellRenderer: params => functionalFlowRenderer(params, showModal),
  },
];

// Monthly deviation columns in a separate file

export const getColumnPropsForSprintSheet = sprintList => [
  {
    headerName: 'Sprint Start Date',
    field: 'sprintStartDate',
    type: 'rightAligned',
    width: '170px',
    valueFormatter: dateFormatter,
  },
  {
    headerName: 'Sprint End Date',
    field: 'sprintEndDate',
    type: 'rightAligned',
    width: '175px',
    valueFormatter: dateFormatter,
  },
  {
    headerName: 'Sprint Name',
    field: 'sprintName',
    cellRenderer: data => {
      const sprintName =
        data.data && data.data.sprintName ? data.data.sprintName : '';
      const sprintUrl =
        data.data && data.data.sprintUrl ? data.data.sprintUrl : '';

      return (
        <a style={{ color: 'blue' }} href={sprintUrl} target="_blank">
          {sprintName}
        </a>
      );
    },
    sortable: false,
    filter: true,
    floatingFilter: true,
    floatingFilterComponent: StatusCustomFilter,
    floatingFilterComponentParams: {
      suppressFilterButton: true,
      field: 'sprintName',
      selectOptions: sprintList,
    },
  },
  {
    headerName: 'Sales Estimates',
    field: 'salesEstimation',
    type: 'rightAligned',
  },
  {
    headerName: 'Sales Deviation',
    headerTooltip: 'Sales Deviation = Sales Estimation - Dev Spent',
    field: 'salesDeviation',
    type: 'rightAligned',
    cellRenderer: salesDeviationRenderer,
  },
  {
    headerName: 'Dev Re-Estimation',
    field: 'developerOriginalEstimation',
    type: 'rightAligned',
  },
  {
    headerName: 'Dev Spent',
    field: 'developerSpent',
    type: 'rightAligned',
  },
  {
    headerName: 'Dev Deviation',
    headerTooltip: 'Dev Deviation = Dev Re-Estimation - Dev Spent',
    field: 'developerDeviation',
    type: 'rightAligned',
    valueFormatter: numberFormatter,
  },
];
