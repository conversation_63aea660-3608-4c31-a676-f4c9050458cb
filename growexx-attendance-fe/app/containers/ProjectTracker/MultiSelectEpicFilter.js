/* eslint-disable react/prop-types */
import { Select } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useCallback,
} from 'react';
import { debounce } from 'lodash';
import { request } from '../../utils/request';
import { API_ENDPOINTS } from '../constants';

const { Option } = Select;

export default forwardRef((props, ref) => {
  const query = new URLSearchParams(window.location.search);
  const [currentValue, setCurrentValue] = useState(
    query.get(props.field) ? query.get(props.field).split(',') : []
  );
  const [epicOptions, setEpicOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchValue) => {
      if (!searchValue || searchValue.length < 2) {
        setEpicOptions([]);
        return;
      }

      setLoading(true);
      try {
        const boardId = new URLSearchParams(window.location.search).get('boardId');
        if (!boardId) {
          setEpicOptions([]);
          return;
        }

        const response = await request(
          `${API_ENDPOINTS.SEARCH_EPIC_NAMES}?boardId=${boardId}&search=${encodeURIComponent(searchValue)}`,
          { method: 'GET' }
        );

        if (response && response.data) {
          const options = response.data.map(epic => ({
            value: epic.name,
            label: epic.name,
            key: epic._id,
          }));
          setEpicOptions(options);
        }
      } catch (error) {
        console.error('Error fetching epic names:', error);
        setEpicOptions([]);
      } finally {
        setLoading(false);
      }
    }, 500),
    []
  );

  useEffect(() => {
    if (searchText) {
      debouncedSearch(searchText);
    }
  }, [searchText, debouncedSearch]);

  useImperativeHandle(ref, () => ({
    onParentModelChanged(parentModel) {
      if (!parentModel) {
        setCurrentValue([]);
      } else {
        const values = parentModel.filter ? parentModel.filter.split(',') : [];
        setCurrentValue(values);
      }
    },
  }));

  const onCustomFilterSelect = (values) => {
    setCurrentValue(values || []);

    if (!values || values.length === 0) {
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged(null, null);
      });
    } else {
      const filterValue = values.join(',');
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged('', filterValue);
      });
    }
  };

  const onSearch = (value) => {
    setSearchText(value);
  };

  const onFocus = () => {
    // Load initial options when focused if no search text
    if (!searchText && epicOptions.length === 0) {
      setSearchText(''); // This will trigger initial load
    }
  };

  return (
    <div>
      <Select
        mode="multiple"
        value={currentValue}
        placeholder="Search and select epic names"
        showSearch
        allowClear
        className="epic-filter multi-select-epic-filter"
        onChange={onCustomFilterSelect}
        onSearch={onSearch}
        onFocus={onFocus}
        loading={loading}
        filterOption={false} // Disable client-side filtering since we're doing server-side search
        notFoundContent={loading ? 'Searching...' : 'No epics found'}
        style={{ width: '100%', minWidth: '200px' }}
        maxTagCount="responsive"
        showArrow
      >
        {epicOptions.map(option => (
          <Option key={option.key} value={option.value}>
            {option.label}
          </Option>
        ))}
      </Select>
    </div>
  );
});
