/* eslint-disable react/prop-types */
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useCallback,
} from 'react';
import { Select } from 'antd';
import { debounce } from 'lodash';
import { request } from '../../utils/request';
import { API_ENDPOINTS } from '../constants';

const { Option } = Select;

export default forwardRef((props, ref) => {
  const query = new URLSearchParams(window.location.search);
  const [currentValue, setCurrentValue] = useState(
    query.get(props.field) ? query.get(props.field).split(',') : []
  );
  const [epicOptions, setEpicOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  // Function to load epics
  const loadEpics = useCallback(async (searchValue = '') => {
    if (!searchValue || searchValue.length < 2) {
      setEpicOptions([]);
      return;
    }

    setLoading(true);
    try {
      const boardId = new URLSearchParams(window.location.search).get('boardId');
      if (!boardId) {
        console.log('No boardId found in URL');
        setEpicOptions([]);
        return;
      }

      const url = `${API_ENDPOINTS.SEARCH_EPIC_NAMES}?boardId=${boardId}&search=${encodeURIComponent(searchValue)}`;
      console.log('Making API call to:', url);

      const response = await request(url, { method: 'GET' });
      console.log('API response:', response);

      if (response && response.data && Array.isArray(response.data)) {
        const options = response.data.map(epic => ({
          value: epic.name,
          label: epic.name,
          key: epic._id,
        }));
        setEpicOptions(options);
        console.log('Set epic options:', options);
      } else {
        console.log('No valid data in response');
        setEpicOptions([]);
      }
    } catch (error) {
      console.error('Error fetching epic names:', error);
      setEpicOptions([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchValue) => {
      loadEpics(searchValue);
    }, 500),
    [loadEpics]
  );

  useEffect(() => {
    if (searchText && searchText.length >= 2) {
      debouncedSearch(searchText);
    } else {
      setEpicOptions([]);
    }
  }, [searchText, debouncedSearch]);

  useImperativeHandle(ref, () => ({
    onParentModelChanged(parentModel) {
      if (!parentModel) {
        setCurrentValue([]);
      } else {
        const values = parentModel.filter ? parentModel.filter.split(',') : [];
        setCurrentValue(values);
      }
    },
  }));

  const onCustomFilterSelect = (values) => {
    setCurrentValue(values || []);

    if (!values || values.length === 0) {
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged(null, null);
      });
    } else {
      const filterValue = values.join(',');
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged('', filterValue);
      });
    }
  };

  const onSearch = (value) => {
    setSearchText(value);
  };

  return (
    <div>
      <Select
        mode="multiple"
        value={currentValue}
        placeholder="Type to search epic names..."
        showSearch
        allowClear
        className="epic-filter multi-select-epic-filter"
        onChange={onCustomFilterSelect}
        onSearch={onSearch}
        loading={loading}
        filterOption={false}
        notFoundContent={
          loading
            ? 'Searching...'
            : searchText && searchText.length >= 2
              ? 'No epics found'
              : 'Type at least 2 characters to search'
        }
        style={{ width: '100%', minWidth: '200px' }}
        maxTagCount="responsive"
        showArrow
      >
        {epicOptions.map(option => (
          <Option key={option.key} value={option.value}>
            {option.label}
          </Option>
        ))}
      </Select>
    </div>
  );
});
