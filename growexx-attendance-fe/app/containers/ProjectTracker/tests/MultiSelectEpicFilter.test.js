import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { IntlProvider } from 'react-intl';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import configureStore from '../../../configureStore';
import MultiSelectEpicFilter from '../MultiSelectEpicFilter';

const history = createMemoryHistory();
const store = configureStore({}, history);

// Mock the request function
jest.mock('../../../utils/request', () => ({
  request: jest.fn(),
}));

const mockProps = {
  field: 'epicName',
  parentFilterInstance: jest.fn(),
};

describe('<MultiSelectEpicFilter />', () => {
  let component;

  beforeEach(() => {
    // Set up URL search params
    Object.defineProperty(window, 'location', {
      value: {
        search: '?boardId=123&epicName=Epic1,Epic2',
      },
      writable: true,
    });

    component = (
      <Provider store={store}>
        <IntlProvider locale="en">
          <ConnectedRouter history={history}>
            <MultiSelectEpicFilter {...mockProps} />
          </ConnectedRouter>
        </IntlProvider>
      </Provider>
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    const { container } = render(component);
    expect(container.querySelector('.multi-select-epic-filter')).toBeTruthy();
  });

  it('should initialize with values from URL', () => {
    const { container } = render(component);
    const select = container.querySelector('.ant-select');
    expect(select).toBeTruthy();
  });

  it('should handle search input', async () => {
    const { container } = render(component);
    const searchInput = container.querySelector('.ant-select-selection-search-input');
    
    if (searchInput) {
      fireEvent.change(searchInput, { target: { value: 'Epic' } });
      
      await waitFor(() => {
        // Should trigger debounced search
        expect(searchInput.value).toBe('Epic');
      });
    }
  });

  it('should handle multiple selection', () => {
    const { container } = render(component);
    const select = container.querySelector('.ant-select');
    expect(select).toBeTruthy();
  });

  it('should call parentFilterInstance when values change', () => {
    const ref = React.createRef();
    const { rerender } = render(
      <Provider store={store}>
        <IntlProvider locale="en">
          <ConnectedRouter history={history}>
            <MultiSelectEpicFilter ref={ref} {...mockProps} />
          </ConnectedRouter>
        </IntlProvider>
      </Provider>
    );

    // Test the imperative handle
    if (ref.current) {
      ref.current.onParentModelChanged({ filter: 'Epic1,Epic2' });
      expect(mockProps.parentFilterInstance).toHaveBeenCalled();
    }
  });
});
