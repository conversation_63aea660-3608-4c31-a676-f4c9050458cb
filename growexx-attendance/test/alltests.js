const dotenv = require("dotenv");
const env = process.env.NODE_ENV || "testing";
dotenv.config({ path: process.env.PWD + "/" + env + ".env" });
global.logger = require("../server/util/logger");
const chai = require("chai");
const chaiHttp = require("chai-http");
const app = require("../index");
chai.use(chaiHttp);
const request = require("supertest");
request(app);

// Start testing
require("./init.test");

// Auth
require("../server/services/signin/test/signin.test");
require("../server/services/forgotPassword/test/forgotPassword.test");

// User
require("../server/services/userProfile/test/userProfile.test");
require("../server/services/listUser/test/listUser.test");
require("../server/services/listMember/test/listMember.test");
require("../server/services/listProjectManager/test/listProjectManager.test");
require("../server/services/getAttendance/test/getAttendance.test");
require("../server/services/downloadAttendance/test/downloadAttendance.test");
require("../server/services/downloadBillingReport/test/downloadBillingReport.test");
require("../server/services/addUser/test/addUser.test");
require("../server/services/editUser/test/editUser.test");
require("../server/services/mentee/test/mentee.test");

// Sprint Report
require("../server/services/sprintReport/test/SprintReport.test");

// Download Project- Sprint report.
require("../server/services/DownloadProjectSprintReport/test/DownloadProjectSprintReport.test");

// Project Sprint Data
require("../server/services/getProjectSprintData/test/projectSprintData.test");

// Project list
require("../server/services/listProject/test/listProject.test");

// User
require("../server/services/changeStatusUser/test/changeStatusUser.test");

// Logs
require("../server/services/fetchJiraLogs/test/fetchJiraLogs.test");

// Jira
require("../server/services/addJiraPortal/test/addJiraPortal.test");
require("../server/services/editJiraPortal/test/editJiraPortal.test");
require("../server/services/changeStatusJiraPortal/test/changeStatusJiraPortal.test");
require("../server/services/listJiraPortal/test/listJiraPortal.test");
require("../server/services/deleteJiraPortal/test/deleteJiraPortal.test");

// Project
require("../server/services/addProject/test/addProject.test");
require("../server/services/editProject/test/editProject.test");
require("../server/services/changeStatusProject/test/changeStatusProject.test");
// Leave
require("../server/services/uploadLeave/test/uploadLeave.test");
require("../server/services/addLeave/test/addLeave.test");

// Kra
require("../server/services/addKra/test/addKra.test");
require("../server/services/updateKra/test/updateKra.test");
require("../server/services/listKra/test/listKra.test");
require("../server/services/uploadKRA/test/uploadKRA.test");
require("../server/services/assignKra/test/assignKra.test");
require("../server/services/assessKra/test/assessKra.test");
require("../server/services/publishKra/test/publishKra.test");
require("../server/services/getKra/test/getKra.test");
require("../server/services/rateKra/test/rateKra.test");
require("../server/services/getKraForManager/test/getKraForManager.test");
require("../server/services/getUserKra/test/getUserKRA.test");
require("../server/services/selfKra/test/selfKraForUser.test");
require("../server/services/rateSelfKra/test/selfKra.test");
require("../server/services/listAssignedKraForAdmin/test/listAssignedKraForAdmin.test");
require("../server/services/downloadKraAttachment/test/downloadKraAttachment.test");
require("../server/services/deleteKra/test/deleteKra.test");
require("../server/services/listAssignViewKra/test/listAssignViewKra.test");
require("../server/services/listKraGroup/test/listKraGroup.test");
require("../server/services/releaseRatingKra/test/relaseRating.test");
require("../server/services/downloadViewRating/test/downloadViewRating.test");
require("../server/services/unfreezeReviewer/test/unfreezeReviewer.test");
require("../server/services/uploadKRAAttachment/test/uploadKRAAttachment.test");
require("../server/services/addCategoryWeightage/test/addCategoryWeightage.test");
require("../server/services/getCategoryWeightage/test/getCategoryWeightage.test");
require("../server/services/notification/test/notification.test");
require("../server/services/feedbackMeeting/test/feedbackMeeting.test");
require("../server/services/listCategoryWeightage/test/listCategoryWeightage.test");
require("../server/services/downloadUserKraData/test/downloadUserKraData.test");
require("../server/services/startFeedbackMeeting/test/startFeedbackMeeting.test");
require("../server/services/downloadPendingKraAssignReport/test/downloadPendingKraAssignReport.test");
require("../server/services/assignKra/test/bulkAssignKra.test");

// Download reports
require("../server/services/downloadPersonDayReport/test/downloadPersonDayReport.test");
require("../server/services/downloadProjectPersonHoursReport/test/downloadPersonHoursReport.test");
require("../server/services/downloadLogsReport/test/downloadLogsReport.test");
require("../server/services/downloadKraStatusReport/test/downloadKraStatusReport.test");

// Maintenance
require("../server/services/maintenance/test/maintenance.test");

// cron
require("../crons/test/getDailyProjectTracker.test");

// Project tracker
require("../server/services/jiraProjectList/test/jiraProjectList.test");
require("../server/services/getProjectTracker/test/getProjectTracker.test");
require("../server/services/projectTrackerHealthCard/test/projectTrackerHealthCard.test");
require("../server/services/monthlyDeviation/test/monthlyDeviation.test");

// Rag
require("../server/services/getRag/test/getRag.test");

// PLI Parameters
require("../server/services/pliParameters/test/pliParameters.test");

// Update Sprint Scores
require("../server/services/updateSprintScores/test/updateSprintScores.test");

// Get PLI Rating By ID
require("../server/services/getPliRatingById/test/getPliRatingById.test.js");

// Get PLI Rating By Employee ID
require("../server/services/getPliRatingByEmployeeId/test/getPliRatingByEmployeeId.test.js");

// Employee Profile
require("../server/services/employeeProfile/test/employeeProfile.test.js");

// End Testing
require("./end.test");

// getProjectsBasedMonth
require("../server/services/getProjectsBasedMonth/test/getProject.test");

//freezepli

require("../server/services/freezePliRating/tests/freezePliRating.test.js");
require("../server/services/freezePliRating/tests/freezePliRatingController.test.js");
describe("Stop server in end", () => {
  it("Server should stop manually to get code coverage", (done) => {
    app.close();
    done();
  });
});
