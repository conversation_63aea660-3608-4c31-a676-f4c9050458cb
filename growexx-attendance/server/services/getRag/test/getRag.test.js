/* eslint-disable quotes */
/* eslint-disable max-len */
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const RagService = require('../ragService');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const admin = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestAdminPayload = {
    token: jwt.sign(admin, process.env.JWT_SECRET, tokenOptionalInfo)
};


describe('List All Rag', () => {
    try {
        it('As a TPM, I should be able get rag details without any filters', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('As a TPM, I should be able get rag details', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag?project=ATLAS&user=65e980f419be74a4bb21335d&start=2024-03-14T&end=2024-03-20')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('We need to modify rag payload such that FE can easily show it in AG grid tables', async () => {
            const originalRagPayload = [
                {
                    _id: '65f179841aaa2a5ebc1100df',
                    boardId: '81',
                    jiraSprintId: '347',
                    project: 'iMadrassa Mobile Application',
                    boardKey: 'IMA',
                    createdAt: '2024-03-13T10:01:40.290Z',
                    effortVariance: 1.57,
                    openCloseRatio: 80,
                    resolvedBugs: 0,
                    sprintEnd: '2024-01-02T07:50:55.070Z',
                    sprintNumber: 'IM Sprint 5',
                    sprintReport: 'https://growexx.atlassian.net/jira/software/c/projects/IMA/boards/81/reports/sprint-retrospective?sprint=347',
                    sprintStart: '2023-12-20T11:17:43.000Z',
                    totalBugs: 0,
                    updatedAt: '2024-03-13T10:01:40.290Z',
                    mitigationPlan: 'Sprint 5',
                    teamLength: 5,
                    member: 'jainil.patel',
                    b2dCount: 0,
                    bugsReported: '0/0',
                    userId: '65e980f419be74a4bb213345'
                },
                {
                    _id: '65f179841aaa2a5ebc1100df',
                    sprintEnd: '2024-01-02T07:50:55.070Z',
                    teamLength: 5,
                    member: 'jainil.patel',
                    b2dCount: 0,
                    openCloseRatio: 78,
                    effortVariance: -1.91
                },
                {
                    _id: '65f179841aaa2a5ebc1100df',
                    sprintEnd: '2024-01-02T07:50:55.070Z',
                    teamLength: 5,
                    member: 'jainil.patel',
                    b2dCount: 0,
                    openCloseRatio: 56,
                    resolvedBugs: 1,
                    effortVariance: -1.91
                },
                {
                    _id: '65f179841aaa2a5ebc1100df',
                    sprintEnd: '2024-01-02T07:50:55.070Z',
                    teamLength: 5,
                    member: 'jainil.patel',
                    b2dCount: 0,
                    openCloseRatio: 80,
                    resolvedBugs: 2,
                    effortVariancePercentage: -1,
                    effortVariance: -1.91
                }
            ];
            const expectedPayload = [
                {
                    '_id': '65f179841aaa2a5ebc1100df',
                    'boardId': '81',
                    'jiraSprintId': '347',
                    'project': 'iMadrassa Mobile Application',
                    'boardKey': 'IMA',
                    'createdAt': '2024-03-13T10:01:40.290Z',
                    'effortVariance': 1.57,
                    'openCloseRatio': 23.08,
                    'resolvedBugs': 0,
                    'sprintEnd': '2024-01-02T07:50:55.070Z',
                    'sprintNumber': 'IM Sprint 5',
                    'sprintReport': 'https://growexx.atlassian.net/jira/software/c/projects/IMA/boards/81/reports/sprint-retrospective?sprint=347',
                    'sprintStart': '2023-12-20T11:17:43.000Z',
                    'totalBugs': 0,
                    'updatedAt': '2024-03-13T10:01:40.290Z',
                    'mitigationPlan': 'Sprint 5',
                    'teamLength': 5,
                    'member': 'jainil.patel',
                    'b2dCount': 0,
                    'bugsReported': '0/0',
                    'userId': '65e980f419be74a4bb213345',
                    'index': 1,
                    'overallRag': 'red',
                    'bugLink': 'https://growexx.atlassian.net/jira/software/c/projects/IMA/issues/?jql=project%20%3D%20%22IMA%22%20and%20type%20%3D%20Bug%20and%20sprint%20%3D%20347%20AND%20status%20NOT%20IN%20%28Done%29%20ORDER%20BY%20created%20DESC'
                }
            ];
            assert.equal(RagService.modifyRagPayload(originalRagPayload)[0].bugLink, expectedPayload[0].bugLink);
        });

        it('As a TPM, I should be able get sprint metrics report', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag/sprint-metrics')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('As a TPM, I should be able get sprint metrics report', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag/sprint-metrics?project=ATLAS&user=65e980f419be74a4bb21335d&start=2024-03-14T&end=2024-03-20')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('We need to modify sprint metrics payload such that FE can easily show it in AG grid tables', async () => {
            const originalRagPayload = [
                {
                    "_id": "65f18c991aaa2a5ebc11163c",
                    "projectName": "ATLAS- The Internal Auditing",
                    "repo": "Overall",
                    "resource": "arijeet-growexxer",
                    "sprintNumber": "ATLS Sprint 5",
                    "__v": 0,
                    "apiCreated": "0",
                    "availableAPI": "0",
                    "bugs": "0",
                    "codeSmell": "2.4",
                    "coverage": "90.3",
                    "coverageRating": "5",
                    "createdAt": "2024-03-13T11:23:05.431Z",
                    "duplication": "3.3",
                    "endDate": "2023-08-14T00:00:00.000Z",
                    "prCount": "21",
                    "prEfficiency": "76.19",
                    "prRating": "3",
                    "rejectedPrCount": "5",
                    "sonarRating": "4",
                    "sprintAverage": "3.25",
                    "startDate": "2023-08-01T00:00:00.000Z",
                    "swaggerFinalRating": "1",
                    "swaggerRating": "0.00",
                    "updatedAt": "2024-03-13T11:23:05.431Z",
                    "vulnerabilities": "0",
                    "users": []
                },
                {
                    "_id": "65f18c991aaa2a5ebc111642",
                    "projectName": "ATLAS- The Internal Auditing",
                    "repo": "Overall",
                    "resource": "brijesh-growexxer",
                    "sprintNumber": "ATLS Sprint 5",
                    "__v": 0,
                    "apiCreated": "0",
                    "availableAPI": "0",
                    "bugs": "0",
                    "codeSmell": "2.4",
                    "coverage": "92.3",
                    "coverageRating": "5",
                    "createdAt": "2024-03-13T11:23:05.438Z",
                    "duplication": "2.75",
                    "endDate": "2023-08-14T00:00:00.000Z",
                    "prCount": "23",
                    "prEfficiency": "86.96",
                    "prRating": "4",
                    "rejectedPrCount": "3",
                    "sonarRating": "4",
                    "sprintAverage": "3.5",
                    "startDate": "2023-08-01T00:00:00.000Z",
                    "swaggerFinalRating": "1",
                    "swaggerRating": "0.00",
                    "updatedAt": "2024-03-13T11:23:05.438Z",
                    "vulnerabilities": "0",
                    "users": []
                }
            ];
            const expectedPayload = [
                {
                    "_id": "65f18c991aaa2a5ebc11163c",
                    "projectName": "ATLAS- The Internal Auditing",
                    "repo": "Overall",
                    "resource": "arijeet-growexxer",
                    "sprintNumber": "ATLS Sprint 5",
                    "__v": 0,
                    "apiCreated": "0",
                    "availableAPI": "0",
                    "bugs": "0",
                    "codeSmell": "2.4",
                    "coverage": "90.3",
                    "coverageRating": "5",
                    "createdAt": "2024-03-13T11:23:05.431Z",
                    "duplication": "3.3",
                    "endDate": "2023-08-14T00:00:00.000Z",
                    "prCount": "21",
                    "prEfficiency": "76.19",
                    "prRating": "3",
                    "rejectedPrCount": "5",
                    "sonarRating": "4",
                    "sprintAverage": "3.25",
                    "startDate": "2023-08-01T00:00:00.000Z",
                    "swaggerFinalRating": "1",
                    "swaggerRating": "0.00",
                    "updatedAt": "2024-03-13T11:23:05.431Z",
                    "vulnerabilities": "0",
                    "users": [],
                    "projectSpan": 5,
                    "index": 0
                }
            ];
            assert.equal(RagService.processSprintMetricsPayload(originalRagPayload)[0].sprintNumber, expectedPayload[0].sprintNumber);
        });

        it('As a TPM, I should be able to download report', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag/download-report')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('As a TPM, I should be able to edit comments', async () => {
            const res = await request(process.env.BASE_URL)
                .patch('/rag')
                .send({ field: 'comments', id: '65f93eafb9331babe6196374', value: 'test' })
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('As a TPM, I must provide id', async () => {
            const res = await request(process.env.BASE_URL)
                .patch('/rag')
                .send({ field: 'sprintNumber', value: 'test' })
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 400);
        });

        it('As a TPM, I must provide value', async () => {
            const res = await request(process.env.BASE_URL)
                .patch('/rag')
                .send({ field: 'comments', id: '65f93eafb9331babe6196374' })
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('As a TPM, I must provide field', async () => {
            const res = await request(process.env.BASE_URL)
                .patch('/rag')
                .send({ id: '65f93eafb9331babe6196374', value: 'test' })
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 400);
        });

        it('As a TPM, I should not be able to edit sprint number', async () => {
            const res = await request(process.env.BASE_URL)
                .patch('/rag')
                .send({ field: 'sprintNumber', id: '65f93eafb9331babe6196374', value: 'test' })
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 400);
        });

        it('As a TPM, I should be able to run cron through api', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag/trigger-cron?projectId=65f93eafb9331babe6196374')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('As a TPM, I should provide id', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag/trigger-cron')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 400);
        });

        it('As a TPM, I should provide correct id', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/rag/trigger-cron?projectId=')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 400);
        });

    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});