const validation = require('../../util/validation');
const INVALID = 'FIELD_NOT_VALID';
const GeneralError = require('../../util/GeneralError');
/**
 * Class represents validations for RAG
 */
class RagValidator extends validation {
    constructor (query, locale) {
        super(locale);
        this.query = query;
    }

    /**
     * @desc This function is being used to validate change rag details
     * <AUTHOR>
     * @since 12/03/2024
     */
    validationChangeRagDetailsPayload (payload) {
        const { id, field, value } = payload;
        if (!id) {
            throw new GeneralError(this.__(this.REQUIRED, 'Project Id'), 400);
        }
        if (!field) {
            throw new GeneralError(this.__(this.REQUIRED, 'Field'), 400);
        }
        if (value == null) {
            throw new GeneralError(this.__(this.REQUIRED, 'Value'), 400);
        }
        if (!Object.values(CONSTANTS.RAG_EDITABLE_COLUMNS).includes(field)) {
            throw new GeneralError(this.__(INVALID, 'Field'), 400);
        }
    }

    validateProjectId (projectId) {
        if (!projectId) {
            throw new GeneralError(this.__(this.REQUIRED, 'projectId', 400));
        }
        super.checkValidMongoId(projectId, 'projectId');
    }
}
module.exports = RagValidator;
