const mongoose = require('mongoose');
const Portal = require('../../models/portal.model');
const User = require('../../models/user.model');
const Logs = require('../../models/logs.model');
const Project = require('../../models/project.model');
const JiraClient = require('jira.js');
const Utils = require('../../util/utilFunctions');
const Email = require('../../util/sendEmail');

/**
 * Class represents services for fetch jira logs.
 */
class FetchJiraLogsService {

    /**
     * @desc This function is being used to fetch jira logs
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @return {Object} response Success response
     */
    static async fetchJiraLogs(req, user) {
        let where = { isActive: 1 };
        let userIds = [];
        let startDate = MOMENT().startOf('month').format('YYYY-MM-DD');
        let endDate = MOMENT().endOf('month').format('YYYY-MM-DD');
        if (req.query.projectId) {
            where = { _id: mongoose.Types.ObjectId(req.query.projectId) };
        }

        if (req.query.startDate) {
            startDate = req.query.startDate;
        }

        if (req.query.endDate) {
            endDate = req.query.endDate;
        }

        if (req.query.userIds) {
            userIds = req.query.userIds;
        }

        const labels = await FetchJiraLogsService.getLabels(user, userIds);
        const portals = await Portal.find(where, { url: 1, name: 1, email: 1, token: 1 }, { lean: true });
        const unauthorizedProjects = [];
        const bulkOps = [];
        const workLogData = [];
        const logPortalPromises = portals.map(portal =>
            FetchJiraLogsService.logPortalDetails(
                portal,
                startDate,
                endDate,
                labels.employees,
                labels.allLabels,
                labels.userList,
                unauthorizedProjects,
                bulkOps,
                workLogData
            )
        );

        await Promise.allSettled(logPortalPromises);

        if (unauthorizedProjects.length > 0) {
            await FetchJiraLogsService.handleUnauthorizedProjects(unauthorizedProjects);
        }

        if (bulkOps.length) {
            await Project.bulkWrite(bulkOps);
        }
        if (workLogData.length) {
            await Logs.insertMany(workLogData);
        }

        return true;
    }

    static async handleUnauthorizedProjects(unauthorizedProjects) {
        const template = 'emailTemplates/deactivationRequest.html';
        const templateVariables = {
            appUrl: process.env.FRONTEND_URL,
            years: MOMENT().format('YYYY'),
            username: 'TEAM'
        };
        const subject = 'Request: Deactivation of unauthorized project';
        let tableData = '';
        for (const p of unauthorizedProjects) {
            await Portal.updateOne({ _id: p.projectId }, { isActive: 0 });
            tableData += `<tr>
                <td
                    style="font-size: 14px;
                    padding-bottom: 40px;
                    color: #000000;
                    border:1px solid #eeeeee;
                    padding:10px;
                    width: 20%;
                    margin:0;
                    text-align: left;"
                >
                    ${p.projectName}
                </td>
                <td 
                    style="font-size: 14px;
                    padding-bottom: 40px;
                    color: #000000;
                    border:1px solid #eeeeee;
                    padding:10px;
                    margin:0;
                    text-align: left;"
                >
                    ${p.email}
                </td>
                <td
                    style="font-size: 14px;
                    padding-bottom: 40px;
                    color: #000000;
                    border:1px solid #eeeeee;
                    padding:10px;
                    margin:0;
                    text-align: left;"
                >
                    ${p.url}
                </td>
            </tr>`;
        }
        await Email.prepareAndSendEmail(
            CONSTANTS.KRA_ADMINS_EMAIL,
            subject,
            template,
            { ...templateVariables, tableData }
        );
    }

    static async logPortalDetails(portal, startDate, endDate, employees, labels, userList, unauthorizedProjects, bulkOps, workLogData) {
        const projectId = portal._id;
        const projectName = portal.name;
        const jira = new JiraClient.Version3Client({
            host: `https://${portal.url}`,
            authentication: {
                basic: {
                    email: portal.email,
                    apiToken: portal.token
                }
            }
        });

        const searchResult = await FetchJiraLogsService.getLogsPagination(jira, startDate, endDate, 0, 100, [], labels);
        if (searchResult === undefined) {
            unauthorizedProjects.push({ projectId, projectName, url: portal.url, email: portal.email });
            return;
        }

        const startOfDate = MOMENT(startDate).startOf('day');
        const endOfDate = MOMENT(endDate).endOf('day');
        await this.deleteList(projectId, startOfDate, endOfDate, userList);

        const approvedLogs = await this.getApprovedLogs(projectId, startOfDate, endOfDate, userList);
        for (const issue of searchResult) {
            const finalLabels = _.intersection(issue.fields.labels, labels);
            if (finalLabels.length) {
                let jiraProjectName = issue.fields.project.name;
                if (String(projectId) === CONSTANTS.PARKSTREET_PROJECT_ID && issue.fields.customfield_10805) {
                    jiraProjectName = issue.fields.customfield_10805.value;
                }
                const jiraIssueUrl = `${issue.self.split('rest/api')[0]}browse/${issue.key}`;
                const userId = _.find(employees, { 'label': [finalLabels[0]] })._id;
                const label = finalLabels[0];
                const jiraTitle = issue.fields.summary;
                const jiraDescription = await FetchJiraLogsService.getJiraDescription(issue.fields.description);

                for (const worklog of issue.fields.worklog.worklogs) {
                    const started = MOMENT(worklog.started);
                    const created = MOMENT(worklog.created);
                    const jiraUserId = worklog.author.accountId;
                    const jiraUserEmail = worklog.author.emailAddress;
                    const jiraUserTimeZone = worklog.author.timeZone;
                    const { timeSpentSeconds } = worklog;
                    const timeSpentHours = Utils.getHoursFromSeconds(timeSpentSeconds);
                    const deviations = Utils.getDeviationPercentace(timeSpentHours);
                    const logDate = Utils.getOriginalDateFromTimeZoneMoment(worklog.started);
                    const issueId = worklog.issueId;
                    const jiraLogId = worklog.id;
                    const jiraProjectId = issue.fields.project.id;
                    if (logDate >= startOfDate && logDate <= endOfDate) {
                        const matchedLogs = approvedLogs.filter(log => log.jiraLogId === jiraLogId
                            && MOMENT(logDate).diff(MOMENT(log.logDate), 'days') == 0);
                        if (matchedLogs.length === 0
                            || (matchedLogs[0].timeSpentSeconds !== timeSpentSeconds)
                            || (MOMENT(matchedLogs[0].logDate).startOf('day').diff(logDate, 'days') !== 0)
                        ) {
                            workLogData.push({
                                userId,
                                label,
                                projectId,
                                issueId,
                                jiraLogId,
                                logDate,
                                projectName,
                                jiraUserId,
                                jiraUserEmail,
                                jiraUserTimeZone,
                                timeSpentSeconds,
                                timeSpentHours,
                                deviations,
                                jiraIssueUrl,
                                jiraProjectName,
                                jiraTitle,
                                jiraDescription,
                                daysDeviation: created.diff(started, 'days') - 1
                            });
                            bulkOps.push({
                                updateOne: {
                                    filter: { projectName: jiraProjectName },
                                    update: { projectName: jiraProjectName, portalId: portal._id, jiraProjectId },
                                    upsert: true
                                }
                            });
                        }
                    }
                }
            }
        }
    }

    static async getJiraDescription(description) {
        if (description) {
            let str = '';
            const contentArr = description?.content[0]?.content || [];
            for (const obj of contentArr) {
                const text = await FetchJiraLogsService.getTextData(obj);
                str = text;
            }
            return str;
        }
        return '';
    }

    static async getTextData(obj) {
        let text = '';
        if (obj.text) {
            if (text.length) {
                text = `${text}, ${obj.text}`;
            } else {
                text = obj.text;
            }
        }
        return text;
    }

    static chunkArray(arr, size) {
        return Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
            arr.slice(i * size, i * size + size)
        );
    }

    static async getLogsPagination(jira, startDate, endDate, startAt, maxResults, result, labels) {
        const labelChunks = FetchJiraLogsService.chunkArray(labels, 100);
        for (let chunk of labelChunks) {
            const JQL = `worklogDate >= "${startDate}" AND worklogDate <= "${endDate}" AND labels in ("${chunk.join('","')}")`;
            try {
                /* initial request to get the total count */
                const initialResult = await jira.issueSearch.searchForIssuesUsingJql({
                    jql: JQL,
                    fieldsByKeys: true,
                    validateQuery: 'strict',
                    fields: ['project', 'worklog', 'labels', 'customfield_10805', 'summary', 'description'],
                    startAt,
                    maxResults
                });

                result.push(...initialResult.issues);
                const totalResults = initialResult.total;
                const remainingResults = totalResults - maxResults;
                const totalRequests = Math.ceil(remainingResults / maxResults);

                // create an array to hold all the promises
                const promises = [];

                for (let i = 1; i <= totalRequests; i++) {
                    const start = startAt + (i * maxResults);
                    promises.push(jira.issueSearch.searchForIssuesUsingJql({
                        jql: JQL,
                        fieldsByKeys: true,
                        validateQuery: 'strict',
                        fields: ['project', 'worklog', 'labels', 'customfield_10805', 'summary', 'description'],
                        startAt: start,
                        maxResults
                    }));
                }

                // Await all promises concurrently
                const searchResults = await Promise.all(promises);

                // Aggregate all results
                searchResults.forEach(searchResult => {
                    result.push(...searchResult.issues);
                });
            } catch (error) {
                CONSOLE_LOGGER.error('error', error);
                const statusCode = error.response && error.response.status;
                if (statusCode === 401) {
                    return undefined;
                }
                return null;
            }
        }
        return result;
    }

    static async getLabels(user, userIds) {
        let userList = [];
        let employeeObject = {
            label: { $exists: true }
        };

        if (userIds.length && (user.role === CONSTANTS.ROLE.ADMIN || user.role === CONSTANTS.ROLE.PM)) {
            const employeeList = _.split(userIds, ',');

            userList = Object.values(employeeList).map(id =>
                mongoose.Types.ObjectId(_.trim(id))
            );

            employeeObject = {
                label: { $exists: true },
                _id: {
                    $in: userList
                }
            };
        } else if (user.role === CONSTANTS.ROLE.PM || user.role === CONSTANTS.ROLE.USER) {
            userList.push(user._id);
            employeeObject._id = user._id;
        } else {
            // Do nothing
        }

        const employees = await User.find(employeeObject, { _id: 1, label: 1 }, { lean: true });
        const labels = employees.map(l => {
            return l.label;
        });

        const allLabels = _.uniq(_.flatten(labels));

        return { allLabels, employees, userList };
    }

    /**
     * @desc This function is being used to delete jira logs
     * <AUTHOR>
     * @since 09/03/2022
     * @param {Object} projectId
     * @param {Object} startOfDate
     * @param {Object} endOfDate
     * @param {Object} userList
     */

    static async deleteList(projectId, startOfDate, endOfDate, userList) {
        const deleteWhere = {
            projectId,
            logDate: { $gte: startOfDate, $lte: endOfDate },
            logStatus: { $ne: CONSTANTS.LOG_STATUS.APPROVED }
        };
        if (userList.length) {
            deleteWhere.userId = {
                $in: userList
            };
        }
        await Logs.deleteMany(deleteWhere);
    }

    /**
     * @desc This function is being used to get approved jira logs
     * <AUTHOR>
     * @since 14/02/2025
     * @param {Object} projectId
     * @param {Object} startOfDate
     * @param {Object} endOfDate
     * @param {Object} userList
     */
    static async getApprovedLogs(projectId, startOfDate, endOfDate, userList) {
        const query = {
            projectId,
            logDate: { $gte: startOfDate, $lte: endOfDate },
            logStatus: { $eq: CONSTANTS.LOG_STATUS.APPROVED }
        };
        if (userList.length) {
            query.userId = {
                $in: userList
            };
        }
        return await Logs.find(query);
    }
}

module.exports = FetchJiraLogsService;