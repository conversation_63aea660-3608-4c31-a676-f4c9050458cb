/* eslint-env mocha */
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const request = require('supertest');
const jwt = require('jsonwebtoken');
const sinon = require('sinon');
chai.use(chaiHttp);

const User = require('../../../models/user.model');

/**
 * Unit tests for employee profile endpoint for PLI
 */
describe('PLI Employee Profile API', () => {
    const tokenOptionalInfo = {
        algorithm: 'HS256',
        expiresIn: 86400
    };

    const testUser = {
        id: '5f083c352a7908662c334532',
        email: '<EMAIL>'
    };

    const userObj = {
        _id: testUser.id,
        firstName: 'Test',
        lastName: 'User',
        email: testUser.email,
        isActive: 1,
        role: 1
    };

    const authToken = jwt.sign(
        testUser,
        process.env.JWT_SECRET,
        tokenOptionalInfo
    );

    const mockUser = {
        _id: '5f4f27e08fbfb620fc1ec970',
        firstName: 'John',
        lastName: 'Doe',
        employeeId: 123,
        businessUnit: 'Engineering',
        designation: 'Software Developer',
        label: ['Developer']
    };

    afterEach(() => {
        sinon.restore();
    });

    const stubAuthMiddleware = () => {
        return sinon.stub(User, 'findOne').callsFake((query) => {
            // Auth middleware check
            if (query && query._id === testUser.id) {
                return {
                    lean: () => ({
                        then: (callback) => {
                            callback(userObj);
                            return { catch: () => {} };
                        }
                    })
                };
            }

            // Employee profile lookup
            if (query && query.employeeId === 123) {
                return {
                    lean: () => mockUser
                };
            } else if (query && query.employeeId === 999) {
                return {
                    lean: () => null
                };
            }

            return {
                lean: () => null
            };
        });
    };

    const makeRequest = async (employeeId, token = authToken) => {
        return request(process.env.BASE_URL)
            .get(`/pli/employee-profile/${employeeId}`)
            .set({ Authorization: token });
    };

    describe('GET /pli/employee-profile/:employeeId', () => {
        it('should return employee profile with formatted DOJ when employee exists', async () => {
            const authStub = stubAuthMiddleware();

            // Override the findOne method for this specific test
            authStub.withArgs({ employeeId: 123 }).returns({
                lean: () => ({
                    ...mockUser,
                    doj: new Date('2020-01-01')
                })
            });

            const res = await makeRequest(123);

            expect(res.status).to.equal(200);
            expect(res.body.status).to.equal(1);
            expect(res.body.data).to.deep.equal({
                name: 'John Doe',
                empId: 123,
                department: 'Engineering',
                designation: 'Developers',
                reportingManager: '',
                reportingManagerId: '',
                doj: '01-Jan-2020',
                pliDuration: '1 Month',
                label: 'Developer',
                menteeId: '5f4f27e08fbfb620fc1ec970'
            });
        });

        it('should handle employee with null DOJ', async () => {
            const authStub = stubAuthMiddleware();

            // Override the findOne method for this specific test
            authStub.withArgs({ employeeId: 123 }).returns({
                lean: () => ({
                    ...mockUser,
                    doj: null
                })
            });

            const res = await makeRequest(123);

            expect(res.status).to.equal(200);
            expect(res.body.status).to.equal(1);
            expect(res.body.data.doj).to.equal('');
        });

        it('should return error when employee is not found', async () => {
            stubAuthMiddleware();

            const res = await makeRequest(999);

            expect(res.status).to.equal(400);
            expect(res.body.status).to.equal(0);
            expect(res.body.message).to.equal('Employee not found');
        });

        it('should validate employee ID is a number', async () => {
            stubAuthMiddleware();

            const res = await makeRequest('abc');

            expect(res.status).to.equal(406);
            expect(res.body.status).to.equal(0);
            expect(res.body.message).to.include(
                'You are not authorized to access this'
            );
        });

        it('should require authentication', async () => {
            const res = await makeRequest(123, '');

            expect(res.status).to.equal(401);
            expect(res.body.status).to.equal(0);
        });

        it('should handle employee with missing business unit', async () => {
            const authStub = stubAuthMiddleware();

            // Override the findOne method for this specific test
            authStub.withArgs({ employeeId: 123 }).returns({
                lean: () => ({
                    ...mockUser,
                    businessUnit: undefined
                })
            });

            const res = await makeRequest(123);

            expect(res.status).to.equal(200);
            expect(res.body.status).to.equal(1);
            expect(res.body.data.department).to.equal('');
        });
    });
});
