const ProjectTrackerValidator = require('./projectTrackerValidator');
const Epic = require('../../models/epic.model');
const Sprint = require('../../models/sprint.model');
const PtCronStatus = require('../../models/ptCronStatus.model');
const mongoose = require('mongoose');
const fetchAndStoreProjectData = require('../../../crons/projectTrackerHelper');
const constants = require('../../util/constants');

/**
 * Class represents services for Get Project Tracker.
 */
class ProjectTrackerService {

    /**
     * @desc This function is being used to get the Project Trakcer List
     * <AUTHOR>
     * @since 24/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async getProjectTracker (req, locale) {
        const boardId = req.query.boardId;
        const Validator = new ProjectTrackerValidator(req.query, locale);
        Validator.validate(boardId);

        const options = {
            page: (req.query.page) ? req.query.page : 1,
            limit: (req.query.limit) ? req.query.limit : 10
        };
        const isPaginate = req.query.isPaginate || 'true';
        let tableData = {};

        const epicStatus = await Epic.aggregate(this.getAggregateParamsForSalesEstimate2(boardId));
        let epicName = [];

        if (req.query.epicStatus) {
            const epicStatus2 = req.query.epicStatus;
            const groupedData = epicStatus.reduce((acc, { jiraEpicId, status }) => {
                if (!acc[status]) {
                    acc[status] = [];
                }
                acc[status].push(jiraEpicId);
                return acc;
            }, {});
            const result = Object.entries(groupedData).map(([status, jiraEpicIds]) => ({
                Status: status,
                JiraEpicIds: jiraEpicIds
            }));
            epicName = result.find(status => status.Status === epicStatus2);
        }

        let aggregateParams;

        aggregateParams = ProjectTrackerService.getProjectTrackerAggregate(req.query, req.query.epicStatus);
        if (req.query.epicStatus && epicName) {
            aggregateParams = ProjectTrackerService.getProjectTrackerAggregateByFilter(req.query, epicName.JiraEpicIds);
        }
        if (isPaginate === 'true') {
            const aggregate = Epic.aggregate(aggregateParams);
            tableData = await Epic.aggregatePaginate(aggregate, options);
        } else {
            tableData = await Epic.aggregate(aggregateParams);
        }
        const sprintAggregationParams = ProjectTrackerService.getSprintFilterData(req.query);
        const sprintData = await Sprint.aggregate(sprintAggregationParams);
        return {
            tableData,
            filterData: {
                sprint: sprintData
            },
            epicStatus
        };
    }
    // gfeature/PA-64-Model-Creation-MigrationeParamsForSalesEstimate2(boardId) {
    /**
     * @desc This function is being used to get the Sprint Filter data parameters.
     * <AUTHOR>
     * @since 06/12/2023
     */
    static getAggregateParamsForSalesEstimate2 (boardId) {
        return [
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(boardId)
                }
            },
            {
                $lookup: {
                    from: 'stories',
                    localField: '_id',
                    foreignField: 'epicId',
                    as: 'stories'
                }
            },
            {
                $project: {
                    _id: 1,
                    epicName: '$name',
                    salesEstimate: 1,
                    jiraEpicId: '$jiraEpicId',
                    stories: {
                        $ifNull: ['$stories', []]
                    }
                }
            },
            {
                $addFields: {
                    epicStatus: {
                        $cond: {
                            if: { $eq: [{ $size: '$stories' }, 0] },
                            then: constants.EPIC_STATUS.TO_DO,
                            else: {
                                $cond: {
                                    if: {
                                        $eq: [
                                            { $size: { $setDifference: ['$stories.status', [constants.EPIC_STATUS.TO_DO]] } },
                                            0
                                        ]
                                    },
                                    then: constants.EPIC_STATUS.TO_DO,
                                    else: {
                                        $cond: {
                                            if: {
                                                $in: [constants.EPIC_STATUS.IN_PROGRESS, { $setUnion: ['$stories.status', []] }]
                                            },
                                            then: constants.EPIC_STATUS.IN_PROGRESS,
                                            else: {
                                                $cond: {
                                                    if: {
                                                        $eq: [
                                                            { $size: { $setDifference: ['$stories.status', [constants.EPIC_STATUS.DONE]] } },
                                                            0
                                                        ]
                                                    },
                                                    then: constants.EPIC_STATUS.DONE,
                                                    else: constants.EPIC_STATUS.IN_PROGRESS
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    epicName: 1,
                    jiraEpicId: 1,
                    status: '$epicStatus'
                }
            }
        ];
    }
    static getSprintFilterData (query) {
        const aggregateParams = [];
        aggregateParams.push(
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(query.boardId)
                }
            },
            {
                $project: {
                    _id: 0,
                    name: 1,
                    startDate: 1
                }
            },
            {
                $sort: {
                    startDate: -1
                }
            }
        );
        return aggregateParams;
    }
    /**
     * @desc This function is being used to get the Project tracker aggregation parameters.
     * <AUTHOR>
     * @since 27/11/2023
     */
    static getProjectTrackerAggregateByFilter (query, JiraEpicIds) {
        const aggregateParams = [];
        aggregateParams.push(
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(query.boardId)
                }
            },
            {
                $lookup: {
                    from: 'stories',
                    localField: '_id',
                    foreignField: 'epicId',
                    as: 'stories'
                }
            },
            {
                $addFields: {
                    totalLoggedEfforts: {
                        $sum: '$stories.loggedEffort'
                    }
                }
            },
            {
                $addFields: {
                    epicDeviation: {
                        $divide: [
                            {
                                $subtract: ['$salesEstimate', '$totalLoggedEfforts']
                            },
                            3600
                        ]
                    }
                }
            },
            {
                $unwind: {
                    path: '$stories'
                }
            },
            {
                $addFields: {
                    extractedStoryNumber: {
                        $toInt: {
                            $arrayElemAt: [
                                {
                                    $split: ['$stories.jiraStoryNo', '-']
                                },
                                1
                            ]
                        }
                    }
                }
            },
            {
                $addFields: {
                    'stories.originalEstimateHour': { $divide: ['$stories.originalEstimate', 3600] },
                    'stories.loggedEffortHour': { $divide: ['$stories.loggedEffort', 3600] },
                    salesEstimateHour: { $divide: ['$salesEstimate', 3600] }
                }
            },
            {
                $lookup: {
                    from: 'sprints',
                    localField: 'stories.sprintId',
                    foreignField: '_id',
                    as: 'sprints'
                }
            },
            {
                $unwind: {
                    path: '$sprints',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $match: {
                    jiraEpicId: {
                        $in: JiraEpicIds
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    epicName: '$name',
                    salesEstimation: { $round: ['$salesEstimateHour', 2] },
                    developerOriginalEstimation: { $round: ['$stories.originalEstimateHour', 2] },
                    developerSpent: { $round: ['$stories.loggedEffortHour', 2] },
                    developerDeviation: {
                        $subtract: [
                            { $round: ['$stories.originalEstimateHour', 2] },
                            { $round: ['$stories.loggedEffortHour', 2] }
                        ]
                    },
                    epicDeviation: { $round: ['$epicDeviation', 2] },
                    sprintName: {
                        $ifNull: ['$sprints.name', '']
                    },
                    sprintUrl: {
                        $ifNull: ['$sprints.sprintUrl', '']
                    },
                    storyNumber: '$stories.jiraStoryNo',
                    storyLink: '$stories.jiraStoryUrl',
                    status: '$stories.status',
                    userStory: '$stories.title',
                    functionalFlow: '$stories.description',
                    startDate: '$stories.startDate',
                    endDate: '$stories.endDate',
                    extractedStoryNumber: 1,
                    jiraEpicUrl: 1,
                    createdAt: 1,
                    jiraEpicId: 1
                }
            }
        );
        aggregateParams.push(...ProjectTrackerService.getSearchFilterParams(query));
        aggregateParams.push(...ProjectTrackerService.getSortingParams(query));
        return aggregateParams;
    }
    static getProjectTrackerAggregate (query) {
        const aggregateParams = [];
        aggregateParams.push(
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(query.boardId)
                }
            },
            {
                $lookup: {
                    from: 'stories',
                    localField: '_id',
                    foreignField: 'epicId',
                    as: 'stories'
                }
            },
            {
                $addFields: {
                    totalLoggedEfforts: {
                        $sum: '$stories.loggedEffort'
                    }
                }
            }, {
                $addFields: {
                    epicDeviation: {
                        $divide: [{
                            $subtract: ['$salesEstimate', '$totalLoggedEfforts']
                        }, 3600]
                    }
                }
            },
            {
                $unwind: {
                    path: '$stories'
                }
            },
            {
                $addFields: {
                    extractedStoryNumber: {
                        $toInt: {
                            $arrayElemAt: [{
                                $split: ['$stories.jiraStoryNo', '-']
                            }, 1]
                        }
                    }
                }
            },
            {
                $addFields: {
                    'stories.originalEstimateHour': { $divide: ['$stories.originalEstimate', 3600] },
                    'stories.loggedEffortHour': { $divide: ['$stories.loggedEffort', 3600] },
                    salesEstimateHour: { $divide: ['$salesEstimate', 3600] }
                }
            },
            {
                $lookup: {
                    from: 'sprints',
                    localField: 'stories.sprintId',
                    foreignField: '_id',
                    as: 'sprints'
                }
            },
            {
                $unwind: {
                    path: '$sprints',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $project: {
                    _id: 1,
                    epicName: '$name',
                    salesEstimation: { $round: ['$salesEstimateHour', 2] },
                    developerOriginalEstimation: { $round: ['$stories.originalEstimateHour', 2] },
                    developerSpent: { $round: ['$stories.loggedEffortHour', 2] },
                    developerDeviation: {
                        $subtract: [{ $round: ['$stories.originalEstimateHour', 2] }, { $round: ['$stories.loggedEffortHour', 2] }]
                    },
                    epicDeviation: { $round: ['$epicDeviation', 2] },
                    sprintName: {
                        $ifNull: ['$sprints.name', '']
                    },
                    sprintUrl: {
                        $ifNull: ['$sprints.sprintUrl', '']
                    },
                    storyNumber: '$stories.jiraStoryNo',
                    storyLink: '$stories.jiraStoryUrl',
                    status: '$stories.status',
                    userStory: '$stories.title',
                    functionalFlow: '$stories.description',
                    startDate: '$stories.startDate',
                    endDate: '$stories.endDate',
                    extractedStoryNumber: 1,
                    jiraEpicUrl: 1,
                    createdAt: 1,
                    jiraEpicId: 1
                }
            }
        );
        aggregateParams.push(...ProjectTrackerService.getSearchFilterParams(query));
        aggregateParams.push(...ProjectTrackerService.getSortingParams(query));
        return aggregateParams;
    }
    /**
     * @desc This function is being used to get the sorting parameters
     * <AUTHOR>
     * @since 27/11/2023
     */
    static getSortingParams (query) {
        const aggregateParams = [];
        const sortingParams = {
            salesEstimation: 'salesEstimation',
            epicDeviation: 'epicDeviation',
            storyNumber: 'extractedStoryNumber',
            developerOriginalEstimation: 'developerOriginalEstimation',
            developerSpent: 'developerSpent',
            developerDeviation: 'developerDeviation',
            startDate: 'startDate',
            endDate: 'endDate'
        };
        if (query.sort && Object.keys(sortingParams).includes(query.sortBy) ) {
            const sort = {};
            sort[sortingParams[query.sortBy]] = parseInt(query.sort);
            aggregateParams.push({
                $sort: sort
            });
        } else {
            aggregateParams.push({
                $sort: {
                    createdAt: 1
                }
            });
        }
        return aggregateParams;
    }
    /**
     * @desc This function is being used to get the search parameters
     * <AUTHOR>
     * @since 27/11/2023
     */
    /**
     * @desc This function escapes special regex characters in a string
     * <AUTHOR>
     * @since 03/06/2025
     * @param {string} text - The text to escape
     * @return {string} - The escaped text
     */
    static escapeRegexSpecialChars (text) {
        // Escape special regex characters: . * + ? ^ $ { } ( ) | [ ] \
        return text.replace(/[.*+?^${}()|\[\]\\]/g, '\\$&');
    }

    /**
     * @desc This function is being used to get the search parameters
     * <AUTHOR>
     * @since 27/11/2023
     */
    static getSearchFilterParams (query) {
        const aggregateParams = [];
        if (query.status) {
            if (query.status === 'In Progress') {
                aggregateParams.push({
                    $match: {
                        status: { $nin: ['To Do', 'Done'] }
                    }
                });
            } else {
                aggregateParams.push({
                    $match: {
                        status: query.status
                    }
                });
            }
        }
        if (query.sprintName) {
            aggregateParams.push({
                $match: {
                    sprintName: query.sprintName
                }
            });
        }
        if (query.epicName) {
            // Handle multiple epic names separated by comma
            const epicNames = query.epicName.split(',').map(name => name.trim()).filter(name => name);

            if (epicNames.length === 1) {
                // Single epic name - use regex for partial matching
                const escapedEpicName = this.escapeRegexSpecialChars(epicNames[0]);
                aggregateParams.push({
                    $match: {
                        epicName: { $regex: `.*${escapedEpicName}.*`, $options: 'i' }
                    }
                });
            } else if (epicNames.length > 1) {
                // Multiple epic names - use exact matching with $in
                aggregateParams.push({
                    $match: {
                        epicName: { $in: epicNames }
                    }
                });
            }
        }
        if (query.storyNumber) {
            const escapedStoryNumber = this.escapeRegexSpecialChars(query.storyNumber);
            aggregateParams.push({
                $match: {
                    storyNumber: { $regex: `.*${escapedStoryNumber}.*`, $options: 'i' }
                }
            });
        }
        return aggregateParams;
    }
    static async triggerCron (req, locale) {
        const projectId = req.query.projectId;
        const Validator = new ProjectTrackerValidator(req.query, locale);
        Validator.validateProjectId(projectId);
        const currentCron = await PtCronStatus.findOne({});
        if (!currentCron.isCronRunning) {
            await fetchAndStoreProjectData(projectId);
        } else {
            throw Error('Cannot trigger cron as one cron job is running');
        }
    }

    static async getCronStatus () {
        return await PtCronStatus.findOne({});
    }

    /**
     * @desc This function is being used to search epic names
     * <AUTHOR>
     * @since 2024
     * @param {Object} req Request
     * @return {Promise<Array>} Array of epic names
     */
    static async searchEpicNames (req) {
        const { boardId, search } = req.query;

        // Validate required parameters
        if (!boardId) {
            throw new Error('Board ID is required');
        }

        const aggregateParams = [
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(boardId)
                }
            }
        ];

        // Add search filter if search term is provided
        if (search && search.length >= 2) {
            // Escape special regex characters in search term
            const escapedSearch = this.escapeRegexSpecialChars(search);
            aggregateParams.push({
                $match: {
                    name: { $regex: `.*${escapedSearch}.*`, $options: 'i' }
                }
            });
        }

        // Add grouping, projection, sorting and limiting
        aggregateParams.push(
            {
                $group: {
                    _id: '$name',
                    epicId: { $first: '$_id' },
                    name: { $first: '$name' }
                }
            },
            {
                $project: {
                    _id: '$epicId',
                    name: '$name'
                }
            },
            {
                $sort: { name: 1 }
            },
            {
                $limit: 20 // Limit results to prevent too many options
            }
        );

        return await Epic.aggregate(aggregateParams);
    }
}

module.exports = ProjectTrackerService;
