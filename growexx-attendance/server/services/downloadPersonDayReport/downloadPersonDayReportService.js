const mongoose = require('mongoose');
const User = require('../../models/user.model');
const Project = require('../../models/project.model');
const BusinessUnit = require('../../models/bu.model');
const { parse } = require('json2csv');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents services for download user attendance.
 */
class DownloadPersonDayReportService {

    static defaultProjectProperties () {
        return {
            employeeId: 1,
            firstName: 1,
            lastName: 1,
            email: 1,
            label: 1,
            role: 1,
            isActive: 1,
            doj: 1
        };
    }

    static logLookupAggegration (startDate, endDate) {
        return {
            from: 'logs',
            let: {
                userId: '$_id',
                startDate,
                endDate
            },
            pipeline: [{
                $match: {
                    $expr: {
                        $and: [
                            { $eq: ['$userId', '$$userId'] },
                            { $gte: ['$logDate', '$$startDate'] },
                            { $lte: ['$logDate', '$$endDate'] }
                        ]
                    }
                }
            }, {
                $group: {
                    _id: '_id',
                    logs: { $sum: '$timeSpentHours' }
                }
            }],
            as: 'logs'
        };
    }

    static leaveLookupAggegration (startDate, endDate) {
        const weekWorkingDayArr = [2, 3, 4, 5, 6];
        return {
            from: 'leaves',
            let: {
                id: '$_id',
                startDate,
                endDate
            },
            pipeline: [{
                $match: {
                    $expr: {
                        $and: [
                            { $eq: ['$userId', '$$id'] },
                            { $gte: ['$leaveDate', '$$startDate'] },
                            { $lte: ['$leaveDate', '$$endDate'] },
                            { $in: [{ $dayOfWeek: '$leaveDate' }, weekWorkingDayArr] }
                        ]
                    }
                }
            },
            {
                $project: {
                    timeSpentHours: 1
                }
            }],
            as: 'leaves'
        };
    }

    /**
     * @desc This function is being used to download person days report
     * <AUTHOR>
     * @since 17/05/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async downloadPersonDayReport (req, user) {
        const where = {
            role: { $ne: CONSTANTS.ROLE.ADMIN },
            label: { $exists: true, $type: 2 },
            isActive: CONSTANTS.STATUS.ACTIVE,
            doj: { $exists: true }
        };
        let buDetails = '';
        if (user.role === CONSTANTS.ROLE.BU) {
            buDetails = await BusinessUnit.findOne({ userId: user._id }, { _id: 1 }).lean();
        }
        const isUserManager = await Project.countDocuments(
            {
                $or: [{ pmUser: { $eq: user._id } },
                { reviewManager: { $eq: user._id } },
                { businessUnitId: { $eq: buDetails?._id } }]
            });
        if (user.role === CONSTANTS.ROLE.ADMIN) {
            if (req.query.userId) {
                where._id = mongoose.Types.ObjectId(req.query.userId);
            }
        } else if (isUserManager) {
            if (req.query.userId) {
                where._id = mongoose.Types.ObjectId(req.query.userId);
            }
        } else {
            // Do Nothing
        }
        const startDate = (req.query.startDate) ? MOMENT(req.query.startDate).startOf('day')
            : MOMENT().startOf('month');
        const endDate = (req.query.endDate) ? MOMENT(req.query.endDate).endOf('day')
            : MOMENT().endOf('month');

        const workingDays = Utils.calculateBusinessDays(startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD'));
        const aggregateParams = DownloadPersonDayReportService.getAggeagateParams(where, startDate._d, endDate._d, workingDays);
        const logs = await User.aggregate(aggregateParams);

        const formattedLogs = [];
        logs.forEach((obj) => {
            const { employeeId, name, leaves, workingDay, actualWorkingDays, logs, logDeviation } = obj;
            formattedLogs.push({ employeeId, name, workingDay, leaves, actualWorkingDays, logs, logDeviation });
        });

        const fields = [{
            label: 'Employee Code',
            value: 'employeeId'
        }, {
            label: 'Employee Name',
            value: 'name'
        }, {
            label: 'Total Working Days',
            value: 'workingDay'
        }, {
            label: 'Leaves',
            value: 'leaves'
        }, {
            label: 'Actual working days',
            value: 'actualWorkingDays'
        }, {
            label: 'Logged Hours',
            value: 'logs'
        }, {
            label: 'Deviation (in percentage)',
            value: 'logDeviation'
        }];

        const opts = { fields, quote: '' };
        const csvData = parse(formattedLogs, opts);
        return {
            headers: [{
                key: 'Content-Type',
                value: 'text/csv'
            }, {
                key: 'Content-Disposition',
                value: 'attachment; filename=persondays.csv'
            }],
            data: csvData
        };
    }

    static getAggeagateParams (where, startDate, endDate, workingDays) {
        const totalHours = (workingDays * CONSTANTS.STANDARD_DAY_HOURS) ? workingDays * CONSTANTS.STANDARD_DAY_HOURS : 1;
        const dayInMs = 24 * 60 * 60 * 1000;
        return [{
            $match: where
        }, {
            $project: {
                ...DownloadPersonDayReportService.defaultProjectProperties(),
                startDate: startDate._d,
                endDate: {
                    $subtract: ['$doj', dayInMs]
                }
            }
        }, {
            $project: {
                ...DownloadPersonDayReportService.defaultProjectProperties(),
                daysBetween: {
                    $add: [{
                        $floor: {
                            $divide: [
                                { $subtract: ['$endDate', '$startDate'] },
                                dayInMs]
                        }
                    }, 1
                    ]
                },
                startDay: {
                    $dayOfWeek: '$startDate'
                },
                endDay: {
                    $dayOfWeek: '$endDate'
                }
            }
        }, {
            $project: {
                ...DownloadPersonDayReportService.defaultProjectProperties(),
                daysBetween: '$daysBetween',
                weeksBetween: {
                    $floor: {
                        $divide: ['$daysBetween', 7]
                    }
                },
                startDay: '$startDay',
                remainder: {
                    $add: [{
                        $subtract: ['$daysBetween',
                            {
                                $multiply: [
                                    {
                                        $floor: {
                                            $divide: ['$daysBetween', 7]
                                        }
                                    },
                                    7]
                            }
                        ]
                    }, 1, '$startDay']
                }
            }
        }, {
            $project: {
                ...DownloadPersonDayReportService.defaultProjectProperties(),
                weekDays: {
                    $subtract: ['$daysBetween',
                        {
                            $add: [
                                {
                                    $multiply: ['$weeksBetween', 2]
                                },
                                {
                                    $cond: [
                                        { $gt: ['$remainder', 7] },
                                        2, {
                                            $cond: [
                                                { $eq: ['$remainder', 7] },
                                                1, 0]
                                        }
                                    ]
                                },
                                {
                                    $cond: [
                                        { $eq: ['$startDay', 1] },
                                        1, 0
                                    ]
                                }
                            ]
                        }
                    ]
                }
            }
        }, {
            $project: {
                ...DownloadPersonDayReportService.defaultProjectProperties(),
                dojDiff: {
                    $cond: [
                        {
                            $and: [
                                { $eq: [{ $month: startDate._d }, { $month: '$doj' }] },
                                { $gt: ['$doj', startDate._d] }
                            ]
                        },
                        '$weekDays',
                        0
                    ]
                }
            }
        }, {
            $lookup: {
                ...DownloadPersonDayReportService.logLookupAggegration(startDate, endDate)
            }
        }, {
            $lookup: {
                ...DownloadPersonDayReportService.leaveLookupAggegration(startDate, endDate)
            }
        }, {
            $project: {
                _id: 1,
                employeeId: 1,
                doj: 1,
                dojDiff: 1,
                logs: 1,
                name: { $concat: ['$firstName', ' ', '$lastName'] },
                leaves: [
                    {
                        _id: '_id',
                        leaves: { $sum: '$leaves.timeSpentHours' }
                    }
                ]
            }
        }, {
            $project: {
                dojDiff: 1,
                doj: 1,
                employeeId: 1,
                name: 1,
                workingDay: { $subtract: [workingDays, '$dojDiff'] },
                logs: { $ifNull: [{ $first: '$logs.logs' }, 0] },
                leaves: {
                    $divide: [{
                        $ifNull: [{ '$first': '$leaves.leaves' }, 0]
                    }, 8]
                },
                actualWorkingDays: {
                    $subtract: [{
                        $subtract: [workingDays, {
                            $divide: [{
                                $ifNull: [{ '$first': '$leaves.leaves' }, 0]
                            }, 8]
                        }]
                    }, '$dojDiff']
                },
                logDeviation: {
                    $ifNull: [{
                        $round: [{
                            $multiply: [{
                                $divide: [{
                                    $subtract: [{
                                        $subtract: [{
                                            $subtract: [totalHours,
                                                { $multiply: ['$dojDiff', 8] }
                                            ]
                                        }, {
                                            '$ifNull': [{ '$first': '$leaves.leaves' }, 0]
                                        }]
                                    }, { $first: '$logs.logs' }]
                                }, {
                                    $cond: [{
                                        $eq: [{
                                            $subtract: [{
                                                $subtract: [totalHours, { $multiply: ['$dojDiff', 8] }
                                                ]
                                            }, {
                                                $ifNull: [{ '$first': '$leaves.leaves' }, 0]
                                            }]
                                        }, 0]
                                    }, 1,
                                    {
                                        $subtract: [{
                                            $subtract: [totalHours, { $multiply: ['$dojDiff', 8] }
                                            ]
                                        }, {
                                            $ifNull: [{ '$first': '$leaves.leaves' }, 0]
                                        }]
                                    }
                                    ]
                                }]
                            }, 100]
                        }, 2]
                    }, 100]
                }
            }
        }, {
            $sort: { logDeviation: -1 }
        }];
    }
}

module.exports = DownloadPersonDayReportService;
