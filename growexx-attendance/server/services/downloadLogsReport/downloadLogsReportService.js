const mongoose = require('mongoose');
const Project = require('../../models/project.model');
const Logs = require('../../models/logs.model');
const BusinessUnit = require('../../models/bu.model');
const { parse } = require('json2csv');

/**
 * Class represents services for download project and/or user wise jira logs
 */
class DownloadLogsReportService {

    /**
     * @desc This function is being used to download project and/or user wise jira log
     * <AUTHOR>
     * @since 29/07/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async downloadLogsReport (req, user) {
        const where = {};
        let buDetails = '';
        if (user.role === CONSTANTS.ROLE.BU) {
            buDetails = await BusinessUnit.findOne({ userId: user._id }, { _id: 1 }).lean();
        }
        const isUserManager = await Project.countDocuments(
            { $or: [ { pmUser: { $eq: user._id } },
                { reviewManager: { $eq: user._id } },
                { businessUnitId: { $eq: buDetails?._id } } ] });
        if (user.role === CONSTANTS.ROLE.ADMIN || isUserManager) {
            if (req.query.userId) {
                where.userId = mongoose.Types.ObjectId(req.query.userId);
            }

            if (req.query.projectId) {
                where.jiraProjectName = { $in: req.query.projectId.split(',') };
            }
        } else {
            // Do nothing
        }

        const startDate = (req.query.startDate) ? MOMENT(req.query.startDate).startOf('day')._d
            : MOMENT().startOf('month')._d;
        const endDate = (req.query.endDate) ? MOMENT(req.query.endDate).endOf('day')._d
            : MOMENT().endOf('month')._d;
        where.logDate = { $gte: startDate, $lte: endDate };
        const aggregateParams = DownloadLogsReportService.getAggeagateParams(where);
        const logs = await Logs.aggregate(aggregateParams);

        const fields = [{
            label: 'Employee code',
            value: 'employeeId'
        }, {
            label: 'Employee name',
            value: 'name'
        }, {
            label: 'Date',
            value: 'logDate'
        }, {
            label: 'Jira project name',
            value: 'jiraProjectName'
        }, {
            label: 'Jira task id',
            value: 'jiraIssueUrl'
        }, {
            label: 'Logged hours',
            value: 'timeSpentHours'
        }, {
            label: 'Jira task title',
            value: 'jiraTitle'
        }, {
            label: 'Jira task description',
            value: 'jiraDescription'
        }];

        const opts = { fields, quote: '' };
        const csvData = parse(logs, opts);
        return {
            headers: [{
                key: 'Content-Type',
                value: 'text/csv'
            }, {
                key: 'Content-Disposition',
                value: 'attachment; filename=jiralogs.csv'
            }],
            data: csvData
        };
    }

    static getAggeagateParams (where) {
        return [{
            $match: where
        }, {
            $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'users'
            }
        }, {
            $replaceRoot:  {
                newRoot: {
                    $mergeObjects: [{ $arrayElemAt: ['$users', 0] }, '$$ROOT']
                }
            }
        }, {
            $match: {
                'users.isActive': CONSTANTS.STATUS.ACTIVE
            }
        }, {
            $project: {
                employeeId: 1,
                name: { $concat: ['$firstName', ' ', '$lastName'] },
                logDate: 1,
                timeSpentHours: 1,
                jiraProjectName: 1,
                jiraIssueUrl: 1,
                jiraTitle: 1,
                jiraDescription: 1
            }
        }, {
            $sort: {
                employeeId: 1,
                logDate: 1
            }
        }];
    }
}

module.exports = DownloadLogsReportService;
