const mongoose = require('mongoose');
const Project = require('../../models/project.model');
const Logs = require('../../models/logs.model');
const BusinessUnit = require('../../models/bu.model');
const { parse } = require('json2csv');

/**
 * Class represents services for download project and/or user wise billing report
 */
class DownloadBillingReportService {

    /**
     * @desc This function is being used to download project and/or user wise billing report
     * <AUTHOR>
     * @since 06/10/2023
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async downloadBillingReport (req, user) {
        const where = {};
        let buDetails;
        const userCheckCondition = [{ pmUser: { $eq: user._id } }, { reviewManager: { $eq: user._id } }];
        if (user.role === CONSTANTS.ROLE.BU) {
            buDetails = await BusinessUnit.findOne({ userId: user._id }, { _id: 1 }).lean();
            userCheckCondition.push({ businessUnitId: { $eq: buDetails?._id } });
        }
        const isUserManager = await Project.countDocuments(
            { $or: userCheckCondition });
        if (user.role === CONSTANTS.ROLE.ADMIN || isUserManager) {
            if (req.query.userId) {
                where.userId = mongoose.Types.ObjectId(req.query.userId);
            }

            if (req.query.projectId) {
                where.jiraProjectName = { $in: req.query.projectId.split(',').map(id => decodeURIComponent(id)) };
            }
        }

        const startDate = (req.query.startDate) ? MOMENT(req.query.startDate).startOf('day')._d
            : MOMENT().startOf('month')._d;
        const endDate = (req.query.endDate) ? MOMENT(req.query.endDate).endOf('day')._d
            : MOMENT().endOf('month')._d;
        where.logDate = { $gte: startDate, $lte: endDate };
        const aggregateParams = DownloadBillingReportService.getAggregateParams(where);
        console.log('================> Billing query ========', JSON.stringify(aggregateParams));
        const logs = await Logs.aggregate(aggregateParams);
        const billingData = DownloadBillingReportService.generateBillingReportData(logs);
        const fields = DownloadBillingReportService.generateDateArray(startDate, endDate);
        fields.unshift({
            label: 'Employee Name',
            value: 'employeeName'
        });
        fields.push({
            label: 'Total',
            value: 'total'
        });

        const opts = { fields, quote: '' };
        const csvData = parse(billingData, opts);
        return {
            headers: [{
                key: 'Content-Type',
                value: 'text/csv'
            }, {
                key: 'Content-Disposition',
                value: 'attachment; filename=billing_sheet.csv'
            }],
            data: csvData
        };
    }

    /**
     * @desc This function is being used to generate date range fields
     * <AUTHOR>
     * @since 09/10/2023
     */
    static generateDateArray (startDate, endDate) {
        const dateArray = [];
        const currentDate = MOMENT(startDate);
        const endDateMoment = MOMENT(endDate);
        while (currentDate.isSameOrBefore(endDateMoment)) {
            dateArray.push({
                label: currentDate.format('DD MMM YYYY'),
                value: currentDate.format('YYYY-MM-DD')
            });
            currentDate.add(1, 'days');
        }
        return dateArray;
    }

    /**
     * @desc This function is being used to generate aggregate params for the data
     * <AUTHOR>
     * @since 09/10/2023
     */
    static getAggregateParams (where) {
        return [{
            $match: where
        }, {
            $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'users'
            }
        }, {
            $replaceRoot: {
                newRoot: {
                    $mergeObjects: [{ $arrayElemAt: ['$users', 0] }, '$$ROOT']
                }
            }
        }, {
            $match: {
                'users.isActive': CONSTANTS.STATUS.ACTIVE
            }
        }, {
            $project: {
                employeeId: 1,
                name: { $concat: ['$firstName', ' ', '$lastName'] },
                logDate: {
                    $dateToString: {
                        format: '%Y-%m-%d', date: '$logDate'
                    }
                },
                timeSpentHours: 1
            }
        }, {
            $sort: {
                employeeId: 1,
                logDate: 1
            }
        }, {
            $group: {
                _id: {
                    name: '$name',
                    logDate: '$logDate'
                },
                timeSpentSum: {
                    $sum: '$timeSpentHours'
                }
            }
        },
        {
            $project: {
                _id: 0,
                timeSpentSum: 1,
                name: '$_id.name',
                logDate: '$_id.logDate'
            }
        },
        {
            $sort: {
                name: 1,
                logDate: 1
            }
        }
        ];
    }

    /**
     * @desc This function is being used to generate the billing report data
     * <AUTHOR>
     * @since 09/10/2023
     */
    static generateBillingReportData (logs) {
        const users = {};
        const dates = { employeeName: 'Total' };
        let total = 0;
        for (const log of logs) {
            if (!users[log.name]) {
                users[log.name] = {
                    employeeName: log.name,
                    total: 0
                };
            }
            if (!dates[log.logDate]) {
                dates[log.logDate] = 0;
            }
            users[log.name][log.logDate] = log.timeSpentSum;
            users[log.name].total += Number(log.timeSpentSum);
            dates[log.logDate] = Number(dates[log.logDate]) + Number(log.timeSpentSum);
            total += Number(log.timeSpentSum);
        }
        dates.total = total;
        return [...Object.values(users), dates];
    }
}

module.exports = DownloadBillingReportService;
